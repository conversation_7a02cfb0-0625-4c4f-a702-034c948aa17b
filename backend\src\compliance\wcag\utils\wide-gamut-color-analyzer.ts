/**
 * Wide Gamut Color Analyzer
 * Extends EnhancedColorAnalyzer with P3, Rec2020, and dynamic color monitoring capabilities
 */

import { Page } from 'puppeteer';
import EnhancedColorAnalyzer, { EnhancedColorAnalysisResult } from './enhanced-color-analyzer';
import logger from '../../../utils/logger';

// Advanced color space libraries with fallback
let ColorJSLib: any = null;

// Window interface extensions for wide gamut analysis
interface WcagWideGamutWindow extends Window {
  wcagWideGamutAnalysis: {
    analyzeColorSpaces: () => Array<{
      element: string;
      type: string;
      color: string;
      colorSpace: string;
    }>;
    detectColorSpace: (colorString: string) => string;
    getElementSelector: (element: Element) => string;
  };
  wcagDynamicColorMonitor: {
    changes: Array<{
      timestamp: number;
      element: string;
      color: string;
      backgroundColor: string;
      trigger: string;
    }>;
    observers: MutationObserver[];
    startMonitoring: (duration: number, threshold: number) => void;
    stopMonitoring: () => void;
    getResults: () => any[];
    getElementSelector: (element: Element) => string;
    recordColorChange: (element: Element, trigger: string) => void;
    setupHoverMonitoring: () => void;
    setupFocusMonitoring: () => void;
  };
  wcagColorPatternAnalysis: {
    analyzePatterns: () => Array<{
      pattern: string;
      description: string;
      frequency: number;
      type: string;
    }>;
  };
}

try {
  ColorJSLib = require('colorjs.io').default;
} catch (error) {
  logger.debug('colorjs.io library not available for wide gamut analysis');
}

export interface WideGamutColorConfig {
  enableP3Analysis: boolean;
  enableRec2020Analysis: boolean;
  enableDynamicMonitoring: boolean;
  enableContextAnalysis: boolean;
  enablePatternRecognition: boolean;
  monitoringDuration: number; // milliseconds
  colorChangeThreshold: number; // deltaE threshold for significant changes
}

export interface WideGamutColorInfo {
  srgb: { r: number; g: number; b: number };
  p3?: { r: number; g: number; b: number };
  rec2020?: { r: number; g: number; b: number };
  oklch?: { l: number; c: number; h: number };
  colorSpace: 'srgb' | 'p3' | 'rec2020' | 'oklch' | 'unknown';
  gamutCoverage: number; // percentage of target gamut covered
  isWideGamut: boolean;
  deltaE?: number; // color difference from sRGB
}

export interface DynamicColorChange {
  timestamp: number;
  element: string;
  previousColor: WideGamutColorInfo;
  newColor: WideGamutColorInfo;
  deltaE: number;
  trigger: 'hover' | 'focus' | 'animation' | 'media-query' | 'script' | 'unknown';
  significance: 'minor' | 'moderate' | 'major';
}

export interface WideGamutAnalysis {
  totalElements: number;
  wideGamutElements: number;
  colorSpaceDistribution: Record<string, number>;
  p3Coverage: number;
  rec2020Coverage: number;
  dynamicChanges: DynamicColorChange[];
  accessibilityImpact: {
    improvedContrast: number;
    degradedContrast: number;
    neutralImpact: number;
  };
  recommendations: string[];
}

export interface DynamicColorAnalysis {
  monitoringDuration: number;
  totalChanges: number;
  significantChanges: number;
  changesByTrigger: Record<string, number>;
  accessibilityImpact: {
    positiveChanges: number;
    negativeChanges: number;
    neutralChanges: number;
  };
  patterns: {
    hoverEffects: DynamicColorChange[];
    focusEffects: DynamicColorChange[];
    animations: DynamicColorChange[];
    mediaQueryChanges: DynamicColorChange[];
  };
}

export interface ColorAccessibilityPattern {
  pattern: string;
  description: string;
  frequency: number;
  accessibilityScore: number;
  recommendations: string[];
}

/**
 * Advanced wide gamut color analyzer with dynamic monitoring
 */
export class WideGamutColorAnalyzer {
  private static wideGamutInstance: WideGamutColorAnalyzer;
  private config: WideGamutColorConfig;
  private colorChangeObserver: DynamicColorChange[] = [];
  private accessibilityPatterns: Map<string, ColorAccessibilityPattern> = new Map();
  private baseColorAnalyzer: EnhancedColorAnalyzer;

  private constructor(config?: Partial<WideGamutColorConfig>) {
    this.baseColorAnalyzer = EnhancedColorAnalyzer.getInstance();

    this.config = {
      enableP3Analysis: config?.enableP3Analysis ?? true,
      enableRec2020Analysis: config?.enableRec2020Analysis ?? true,
      enableDynamicMonitoring: config?.enableDynamicMonitoring ?? true,
      enableContextAnalysis: config?.enableContextAnalysis ?? true,
      enablePatternRecognition: config?.enablePatternRecognition ?? true,
      monitoringDuration: config?.monitoringDuration || 5000, // 5 seconds
      colorChangeThreshold: config?.colorChangeThreshold || 2.0, // deltaE threshold
    };

    logger.info('🌈 Wide Gamut Color Analyzer initialized', {
      p3Analysis: this.config.enableP3Analysis,
      rec2020Analysis: this.config.enableRec2020Analysis,
      dynamicMonitoring: this.config.enableDynamicMonitoring,
    });
  }

  static getWideGamutInstance(config?: Partial<WideGamutColorConfig>): WideGamutColorAnalyzer {
    if (!WideGamutColorAnalyzer.wideGamutInstance) {
      WideGamutColorAnalyzer.wideGamutInstance = new WideGamutColorAnalyzer(config);
    }
    return WideGamutColorAnalyzer.wideGamutInstance;
  }

  /**
   * Analyze wide gamut colors across the page
   */
  async analyzeWideGamutColors(page: Page): Promise<WideGamutAnalysis> {
    logger.debug('🌈 Starting wide gamut color analysis');

    // Get base color analysis
    const baseAnalysis = await this.baseColorAnalyzer.analyzePageContrast(page);

    // Inject wide gamut analysis functions
    await this.injectWideGamutAnalysisFunctions(page);

    // Analyze color spaces
    const colorSpaceData = await page.evaluate(() => {
      const wcagWindow = window as unknown as WcagWideGamutWindow;
      return wcagWindow.wcagWideGamutAnalysis.analyzeColorSpaces();
    });

    // Process wide gamut analysis
    const wideGamutElements = baseAnalysis.filter((element: EnhancedColorAnalysisResult) =>
      this.isWideGamutElement(element),
    );

    const colorSpaceDistribution = this.calculateColorSpaceDistribution(colorSpaceData);
    const p3Coverage = this.calculateGamutCoverage(colorSpaceData, 'p3');
    const rec2020Coverage = this.calculateGamutCoverage(colorSpaceData, 'rec2020');

    // Analyze accessibility impact
    const accessibilityImpact = this.analyzeAccessibilityImpact(baseAnalysis, wideGamutElements);

    // Generate recommendations
    const recommendations = this.generateWideGamutRecommendations(
      wideGamutElements.length,
      baseAnalysis.length,
      colorSpaceDistribution,
      accessibilityImpact,
    );

    return {
      totalElements: baseAnalysis.length,
      wideGamutElements: wideGamutElements.length,
      colorSpaceDistribution,
      p3Coverage,
      rec2020Coverage,
      dynamicChanges: [], // Will be populated by dynamic monitoring
      accessibilityImpact,
      recommendations,
    };
  }

  /**
   * Monitor dynamic color changes over time
   */
  async monitorDynamicColors(page: Page, duration?: number): Promise<DynamicColorAnalysis> {
    const monitoringDuration = duration || this.config.monitoringDuration;
    logger.debug(`🔄 Starting dynamic color monitoring for ${monitoringDuration}ms`);

    // Clear previous observations
    this.colorChangeObserver = [];

    // Inject dynamic monitoring functions
    await this.injectDynamicMonitoringFunctions(page);

    // Start monitoring
    await page.evaluate(
      (duration: number, threshold: number) => {
        const wcagWindow = window as unknown as WcagWideGamutWindow;
        wcagWindow.wcagDynamicColorMonitor.startMonitoring(duration, threshold);
      },
      monitoringDuration,
      this.config.colorChangeThreshold,
    );

    // Wait for monitoring to complete
    await new Promise((resolve) => setTimeout(resolve, monitoringDuration + 1000));

    // Collect results
    const dynamicChanges = await page.evaluate(() => {
      const wcagWindow = window as unknown as WcagWideGamutWindow;
      return wcagWindow.wcagDynamicColorMonitor.getResults();
    });

    // Process and categorize changes
    const processedChanges = this.processDynamicChanges(dynamicChanges);
    const patterns = this.categorizeDynamicPatterns(processedChanges);
    const accessibilityImpact = this.analyzeDynamicAccessibilityImpact(processedChanges);

    return {
      monitoringDuration,
      totalChanges: processedChanges.length,
      significantChanges: processedChanges.filter((c) => c.significance !== 'minor').length,
      changesByTrigger: this.countChangesByTrigger(processedChanges),
      accessibilityImpact,
      patterns,
    };
  }

  /**
   * Analyze context-aware color usage patterns
   */
  async analyzeColorPatterns(page: Page): Promise<ColorAccessibilityPattern[]> {
    if (!this.config.enablePatternRecognition) {
      return [];
    }

    logger.debug('🎨 Analyzing color accessibility patterns');

    // Inject pattern analysis functions
    await this.injectPatternAnalysisFunctions(page);

    // Analyze common patterns
    const patternData = await page.evaluate(() => {
      const wcagWindow = window as unknown as WcagWideGamutWindow;
      return wcagWindow.wcagColorPatternAnalysis.analyzePatterns();
    });

    // Process patterns
    const patterns = this.processColorPatterns(patternData);

    // Update pattern cache
    patterns.forEach((pattern) => {
      this.accessibilityPatterns.set(pattern.pattern, pattern);
    });

    return patterns;
  }

  /**
   * Get enhanced color analysis with wide gamut support
   */
  async analyzeElementWideGamut(
    page: Page,
    selector: string,
  ): Promise<
    | (EnhancedColorAnalysisResult & {
        wideGamut: WideGamutColorInfo;
        dynamicBehavior?: DynamicColorAnalysis;
      })
    | null
  > {
    // Get base analysis
    const elementData = { selector, text: '', element: selector };
    const baseAnalysis = await this.baseColorAnalyzer.analyzeElementContrast(page, elementData);
    if (!baseAnalysis) return null;

    // Get wide gamut information
    const wideGamutInfo = await this.getElementWideGamutInfo(page, selector);

    // Optional dynamic analysis
    let dynamicBehavior: DynamicColorAnalysis | undefined;
    if (this.config.enableDynamicMonitoring) {
      dynamicBehavior = await this.monitorElementDynamicColors(page, selector);
    }

    return {
      ...baseAnalysis,
      wideGamut: wideGamutInfo,
      dynamicBehavior,
    };
  }

  /**
   * Private helper methods
   */
  private async injectWideGamutAnalysisFunctions(page: Page): Promise<void> {
    await page.evaluate(() => {
      const wcagWindow = window as unknown as WcagWideGamutWindow;
      wcagWindow.wcagWideGamutAnalysis = {
        analyzeColorSpaces() {
          const elements = document.querySelectorAll('*');
          const colorSpaceData: Array<{
            element: string;
            type: string;
            color: string;
            colorSpace: string;
          }> = [];

          elements.forEach((element: Element) => {
            const computedStyle = window.getComputedStyle(element as HTMLElement);
            const color = computedStyle.color;
            const backgroundColor = computedStyle.backgroundColor;

            if (color && color !== 'rgba(0, 0, 0, 0)') {
              colorSpaceData.push({
                element: this.getElementSelector(element),
                type: 'text',
                color,
                colorSpace: this.detectColorSpace(color),
              });
            }

            if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)') {
              colorSpaceData.push({
                element: this.getElementSelector(element),
                type: 'background',
                color: backgroundColor,
                colorSpace: this.detectColorSpace(backgroundColor),
              });
            }
          });

          return colorSpaceData;
        },

        detectColorSpace(colorString: string): string {
          if (colorString.includes('color(display-p3')) return 'p3';
          if (colorString.includes('color(rec2020')) return 'rec2020';
          if (colorString.includes('oklch(')) return 'oklch';
          if (colorString.includes('lch(')) return 'lch';
          return 'srgb';
        },

        getElementSelector(element: Element): string {
          if (element.id) return `#${element.id}`;
          if (element.className) return `.${element.className.split(' ')[0]}`;
          return element.tagName.toLowerCase();
        },
      };
    });
  }

  private async injectDynamicMonitoringFunctions(page: Page): Promise<void> {
    await page.evaluate(() => {
      const wcagWindow = window as unknown as WcagWideGamutWindow;
      wcagWindow.wcagDynamicColorMonitor = {
        changes: [],
        observers: [] as MutationObserver[],

        startMonitoring(duration: number, _threshold: number) {
          this.changes = [];

          // Monitor style changes
          const styleObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (
                mutation.type === 'attributes' &&
                (mutation.attributeName === 'style' || mutation.attributeName === 'class')
              ) {
                this.recordColorChange(mutation.target as Element, 'style-change');
              }
            });
          });

          // Monitor all elements for style changes
          document.querySelectorAll('*').forEach((element) => {
            styleObserver.observe(element, {
              attributes: true,
              attributeFilter: ['style', 'class'],
            });
          });

          this.observers.push(styleObserver);

          // Monitor hover effects
          this.setupHoverMonitoring();

          // Monitor focus effects
          this.setupFocusMonitoring();

          // Stop monitoring after duration
          setTimeout(() => {
            this.stopMonitoring();
          }, duration);
        },

        setupHoverMonitoring() {
          document.addEventListener('mouseover', (event) => {
            this.recordColorChange(event.target as Element, 'hover');
          });
        },

        setupFocusMonitoring() {
          document.addEventListener('focusin', (event) => {
            this.recordColorChange(event.target as Element, 'focus');
          });
        },

        recordColorChange(element: Element, trigger: string) {
          const computedStyle = window.getComputedStyle(element as HTMLElement);
          const color = computedStyle.color;
          const backgroundColor = computedStyle.backgroundColor;

          this.changes.push({
            timestamp: Date.now(),
            element: this.getElementSelector(element),
            color,
            backgroundColor,
            trigger,
          });
        },

        stopMonitoring() {
          this.observers.forEach((observer) => observer.disconnect());
          this.observers = [];
        },

        getResults() {
          return this.changes;
        },

        getElementSelector(element: Element): string {
          if (element.id) return `#${element.id}`;
          if (element.className) return `.${element.className.split(' ')[0]}`;
          return element.tagName.toLowerCase();
        },
      };
    });
  }

  private async injectPatternAnalysisFunctions(page: Page): Promise<void> {
    await page.evaluate(() => {
      const wcagWindow = window as unknown as WcagWideGamutWindow;
      wcagWindow.wcagColorPatternAnalysis = {
        analyzePatterns() {
          const patterns: Array<{
            pattern: string;
            description: string;
            frequency: number;
            type: string;
          }> = [];

          // Analyze button color patterns
          this.analyzeButtonPatterns(patterns);

          // Analyze link color patterns
          this.analyzeLinkPatterns(patterns);

          // Analyze form element patterns
          this.analyzeFormPatterns(patterns);

          return patterns;
        },

        analyzeButtonPatterns(
          patterns: Array<{
            pattern: string;
            description: string;
            frequency: number;
            type: string;
          }>,
        ) {
          const buttons = document.querySelectorAll(
            'button, [role="button"], input[type="button"], input[type="submit"]',
          );
          const buttonColors = new Map<string, number>();

          buttons.forEach((button) => {
            const style = window.getComputedStyle(button as HTMLElement);
            const colorKey = `${style.color}-${style.backgroundColor}`;
            buttonColors.set(colorKey, (buttonColors.get(colorKey) || 0) + 1);
          });

          buttonColors.forEach((frequency, colorKey) => {
            patterns.push({
              pattern: 'button-colors',
              description: `Button color combination: ${colorKey}`,
              frequency,
              type: 'button',
            });
          });
        },

        analyzeLinkPatterns(
          patterns: Array<{
            pattern: string;
            description: string;
            frequency: number;
            type: string;
          }>,
        ) {
          const links = document.querySelectorAll('a');
          const linkStates = new Map<string, number>();

          links.forEach((link) => {
            const style = window.getComputedStyle(link);
            const stateKey = `${style.color}-${style.textDecoration}`;
            linkStates.set(stateKey, (linkStates.get(stateKey) || 0) + 1);
          });

          linkStates.forEach((frequency, stateKey) => {
            patterns.push({
              pattern: 'link-states',
              description: `Link state styling: ${stateKey}`,
              frequency,
              type: 'link',
            });
          });
        },

        analyzeFormPatterns(patterns: any[]) {
          const formElements = document.querySelectorAll('input, select, textarea');
          const formColors = new Map<string, number>();

          formElements.forEach((element) => {
            const style = window.getComputedStyle(element as HTMLElement);
            const colorKey = `${style.color}-${style.backgroundColor}-${style.borderColor}`;
            formColors.set(colorKey, (formColors.get(colorKey) || 0) + 1);
          });

          formColors.forEach((frequency, colorKey) => {
            patterns.push({
              pattern: 'form-colors',
              description: `Form element colors: ${colorKey}`,
              frequency,
              type: 'form',
            });
          });
        },
      };
    });
  }

  private isWideGamutElement(element: EnhancedColorAnalysisResult): boolean {
    // Check if element uses wide gamut colors
    const textColor = element.text.effectiveColor;
    const bgColor = element.background.effectiveColor;

    return this.isWideGamutColor(textColor) || this.isWideGamutColor(bgColor);
  }

  private isWideGamutColor(color: any): boolean {
    // Simple heuristic: check if color values exceed sRGB range
    if (!color || typeof color.r !== 'number') return false;

    // Check for values outside typical sRGB range (0-255)
    return (
      color.r > 255 || color.g > 255 || color.b > 255 || color.r < 0 || color.g < 0 || color.b < 0
    );
  }

  private calculateColorSpaceDistribution(colorSpaceData: any[]): Record<string, number> {
    const distribution: Record<string, number> = {
      srgb: 0,
      p3: 0,
      rec2020: 0,
      oklch: 0,
      lch: 0,
      unknown: 0,
    };

    colorSpaceData.forEach((data) => {
      const colorSpace = data.colorSpace || 'unknown';
      distribution[colorSpace] = (distribution[colorSpace] || 0) + 1;
    });

    return distribution;
  }

  private calculateGamutCoverage(colorSpaceData: any[], targetGamut: 'p3' | 'rec2020'): number {
    const totalColors = colorSpaceData.length;
    if (totalColors === 0) return 0;

    const targetColors = colorSpaceData.filter((data) => data.colorSpace === targetGamut).length;
    return (targetColors / totalColors) * 100;
  }

  private analyzeAccessibilityImpact(
    _baseAnalysis: EnhancedColorAnalysisResult[],
    wideGamutElements: EnhancedColorAnalysisResult[],
  ): WideGamutAnalysis['accessibilityImpact'] {
    let improvedContrast = 0;
    let degradedContrast = 0;
    let neutralImpact = 0;

    wideGamutElements.forEach((element) => {
      const contrastRatio = element.contrast.ratio;

      // Estimate impact based on contrast ratio
      if (contrastRatio > 7) {
        improvedContrast++;
      } else if (contrastRatio < 3) {
        degradedContrast++;
      } else {
        neutralImpact++;
      }
    });

    return {
      improvedContrast,
      degradedContrast,
      neutralImpact,
    };
  }

  private generateWideGamutRecommendations(
    wideGamutCount: number,
    totalCount: number,
    distribution: Record<string, number>,
    impact: WideGamutAnalysis['accessibilityImpact'],
  ): string[] {
    const recommendations: string[] = [];
    const wideGamutPercentage = (wideGamutCount / totalCount) * 100;

    if (wideGamutPercentage > 20) {
      recommendations.push(
        'High usage of wide gamut colors detected - ensure fallbacks for older displays',
      );
    }

    if (distribution.p3 > 0) {
      recommendations.push('P3 color space detected - test on both P3 and sRGB displays');
    }

    if (distribution.rec2020 > 0) {
      recommendations.push(
        'Rec2020 color space detected - very limited display support, consider alternatives',
      );
    }

    if (impact.degradedContrast > 0) {
      recommendations.push(
        `${impact.degradedContrast} elements may have reduced contrast on standard displays`,
      );
    }

    if (impact.improvedContrast > impact.degradedContrast) {
      recommendations.push(
        'Wide gamut colors generally improve accessibility - good implementation',
      );
    }

    return recommendations;
  }

  private processDynamicChanges(rawChanges: any[]): DynamicColorChange[] {
    const processedChanges: DynamicColorChange[] = [];
    const changeMap = new Map<string, any>();

    // Group changes by element
    rawChanges.forEach((change) => {
      const key = `${change.element}-${change.trigger}`;
      if (!changeMap.has(key)) {
        changeMap.set(key, []);
      }
      changeMap.get(key).push(change);
    });

    // Process each group
    changeMap.forEach((changes, _key) => {
      if (changes.length >= 2) {
        const first = changes[0];
        const last = changes[changes.length - 1];

        const deltaE = this.calculateDeltaE(first.color, last.color);
        const significance = this.determineChangeSignificance(deltaE);

        processedChanges.push({
          timestamp: first.timestamp,
          element: first.element,
          previousColor: this.parseColorToWideGamut(first.color),
          newColor: this.parseColorToWideGamut(last.color),
          deltaE,
          trigger: first.trigger,
          significance,
        });
      }
    });

    return processedChanges;
  }

  private categorizeDynamicPatterns(
    changes: DynamicColorChange[],
  ): DynamicColorAnalysis['patterns'] {
    return {
      hoverEffects: changes.filter((c) => c.trigger === 'hover'),
      focusEffects: changes.filter((c) => c.trigger === 'focus'),
      animations: changes.filter((c) => c.trigger === 'animation'),
      mediaQueryChanges: changes.filter((c) => c.trigger === 'media-query'),
    };
  }

  private analyzeDynamicAccessibilityImpact(
    changes: DynamicColorChange[],
  ): DynamicColorAnalysis['accessibilityImpact'] {
    let positiveChanges = 0;
    let negativeChanges = 0;
    let neutralChanges = 0;

    changes.forEach((change) => {
      // Simple heuristic based on deltaE and color properties
      if (change.deltaE > 10) {
        if (change.significance === 'major') {
          negativeChanges++; // Major changes might be disruptive
        } else {
          positiveChanges++; // Moderate changes might improve visibility
        }
      } else {
        neutralChanges++;
      }
    });

    return {
      positiveChanges,
      negativeChanges,
      neutralChanges,
    };
  }

  private countChangesByTrigger(changes: DynamicColorChange[]): Record<string, number> {
    const counts: Record<string, number> = {};

    changes.forEach((change) => {
      counts[change.trigger] = (counts[change.trigger] || 0) + 1;
    });

    return counts;
  }

  private processColorPatterns(patternData: any[]): ColorAccessibilityPattern[] {
    return patternData.map((data) => ({
      pattern: data.pattern,
      description: data.description,
      frequency: data.frequency,
      accessibilityScore: this.calculatePatternAccessibilityScore(data),
      recommendations: this.generatePatternRecommendations(data),
    }));
  }

  private calculatePatternAccessibilityScore(patternData: any): number {
    // Simple scoring based on frequency and type
    let score = 50; // Base score

    if (patternData.type === 'button' && patternData.frequency > 5) {
      score += 20; // Consistent button styling is good
    }

    if (patternData.type === 'link' && patternData.description.includes('underline')) {
      score += 15; // Underlined links are accessible
    }

    return Math.min(score, 100);
  }

  private generatePatternRecommendations(patternData: any): string[] {
    const recommendations: string[] = [];

    if (patternData.frequency === 1) {
      recommendations.push('Consider standardizing this color pattern across similar elements');
    }

    if (patternData.type === 'button' && patternData.frequency > 10) {
      recommendations.push('High button usage - ensure consistent hover and focus states');
    }

    if (patternData.type === 'link' && !patternData.description.includes('underline')) {
      recommendations.push('Consider adding underlines or other visual indicators for links');
    }

    return recommendations;
  }

  private async getElementWideGamutInfo(page: Page, selector: string): Promise<WideGamutColorInfo> {
    const colorInfo = await page.evaluate((sel: string) => {
      const element = document.querySelector(sel) as HTMLElement;
      if (!element) return null;

      const style = window.getComputedStyle(element);
      return {
        color: style.color,
        backgroundColor: style.backgroundColor,
      };
    }, selector);

    if (!colorInfo) {
      return this.getDefaultWideGamutInfo();
    }

    return this.parseColorToWideGamut(colorInfo.color);
  }

  private async monitorElementDynamicColors(
    page: Page,
    selector: string,
  ): Promise<DynamicColorAnalysis> {
    // Simplified element-specific monitoring
    const changes = await page.evaluate(
      (sel: string, duration: number) => {
        return new Promise((resolve) => {
          const changes: any[] = [];
          const element = document.querySelector(sel) as HTMLElement;

          if (!element) {
            resolve(changes);
            return;
          }

          const initialStyle = window.getComputedStyle(element);
          const initialColor = initialStyle.color;

          // Monitor for changes
          const observer = new MutationObserver(() => {
            const newStyle = window.getComputedStyle(element);
            if (newStyle.color !== initialColor) {
              changes.push({
                timestamp: Date.now(),
                previousColor: initialColor,
                newColor: newStyle.color,
                trigger: 'style-change',
              });
            }
          });

          observer.observe(element, {
            attributes: true,
            attributeFilter: ['style', 'class'],
          });

          setTimeout(() => {
            observer.disconnect();
            resolve(changes);
          }, duration);
        });
      },
      selector,
      2000,
    );

    return {
      monitoringDuration: 2000,
      totalChanges: (changes as any[]).length,
      significantChanges: 0,
      changesByTrigger: { 'style-change': (changes as any[]).length },
      accessibilityImpact: { positiveChanges: 0, negativeChanges: 0, neutralChanges: 0 },
      patterns: { hoverEffects: [], focusEffects: [], animations: [], mediaQueryChanges: [] },
    };
  }

  private parseColorToWideGamut(colorString: string): WideGamutColorInfo {
    // Parse color string and convert to wide gamut info
    // This is a simplified implementation
    const defaultInfo = this.getDefaultWideGamutInfo();

    if (!colorString || colorString === 'rgba(0, 0, 0, 0)') {
      return defaultInfo;
    }

    // Use ColorJS.io if available for advanced parsing
    if (ColorJSLib) {
      try {
        const color = new ColorJSLib(colorString);
        const srgb = color.to('srgb');

        return {
          srgb: { r: srgb.r * 255, g: srgb.g * 255, b: srgb.b * 255 },
          colorSpace: color.space.id as any,
          gamutCoverage: this.calculateGamutCoverageForColor(color),
          isWideGamut: color.space.id !== 'srgb',
          deltaE: color.deltaE(new ColorJSLib('srgb', [srgb.r, srgb.g, srgb.b])),
        };
      } catch (error) {
        logger.debug('ColorJS.io parsing failed, using fallback', { error });
      }
    }

    return defaultInfo;
  }

  private getDefaultWideGamutInfo(): WideGamutColorInfo {
    return {
      srgb: { r: 0, g: 0, b: 0 },
      colorSpace: 'srgb',
      gamutCoverage: 0,
      isWideGamut: false,
    };
  }

  private calculateDeltaE(color1: string, color2: string): number {
    // Simplified deltaE calculation
    // In a real implementation, this would use proper color science
    if (color1 === color2) return 0;

    // Simple RGB difference as approximation
    const rgb1 = this.parseSimpleRGB(color1);
    const rgb2 = this.parseSimpleRGB(color2);

    const deltaR = rgb1.r - rgb2.r;
    const deltaG = rgb1.g - rgb2.g;
    const deltaB = rgb1.b - rgb2.b;

    return Math.sqrt(deltaR * deltaR + deltaG * deltaG + deltaB * deltaB) / 10;
  }

  private parseSimpleRGB(colorString: string): { r: number; g: number; b: number } {
    const match = colorString.match(/rgba?\(([^)]+)\)/);
    if (match) {
      const values = match[1].split(',').map((v) => parseFloat(v.trim()));
      return { r: values[0] || 0, g: values[1] || 0, b: values[2] || 0 };
    }
    return { r: 0, g: 0, b: 0 };
  }

  private determineChangeSignificance(deltaE: number): 'minor' | 'moderate' | 'major' {
    if (deltaE < 2) return 'minor';
    if (deltaE < 10) return 'moderate';
    return 'major';
  }

  private calculateGamutCoverageForColor(color: any): number {
    // Simplified gamut coverage calculation
    try {
      const p3Color = color.to('p3');
      const srgbColor = color.to('srgb');

      // Simple heuristic: if P3 and sRGB are different, it's using wider gamut
      const deltaE = new ColorJSLib(p3Color).deltaE(new ColorJSLib(srgbColor));
      return Math.min(deltaE * 10, 100); // Convert to percentage
    } catch (error) {
      return 0;
    }
  }
}

export default WideGamutColorAnalyzer;
